{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 本筆記將建立ResNet與DenseNet模型。我們練習的資料集為CIFAR10。\n", "\n", "\n", "#### 核心概念: \n", "\n", "* 以```subclassing API```來構築模型。\n", "* 學會建構和訓練ResNet, DenseNet。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CIFAR10資料集：https://www.cs.toronto.edu/~kriz/cifar.html"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <a name=00>索引</a>\n", "\n", "* [載入圖片至電腦記憶體](#01)\n", "* [將圖片做resize以及normalization](#02)\n", "* [建造```conv block``` 並疊加成為CNN模型](#03)\n", "* [建造```residual block``` 並疊加成為ResNet](#04)\n", "* [微調```residual block```成為```dense block```，並建立出DenseNet](#05)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "os.environ[\"KERAS_BACKEND\"] = \"torch\"\n", "\n", "import keras\n", "\n", "keras.config.backend()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.set()\n", "import pandas as pd\n", "\n", "from sklearn.metrics import classification_report\n", "import json\n", "import pickle"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='01'>載入圖片至電腦記憶體 </a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["首先看一下包含資料集的資料夾有什麼內容："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! ls -hl ../datasets/cifar-10-batches-py/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["data_batch_1, data_batch_2,..data_batch_5以及test_batch是以binary的方式儲存在硬碟裡。以下我們寫幾個函數，用以載入這些binary格式的圖檔至電腦內的記憶體中，並且將圖的以矩陣的方式儲存。這些圖矩陣的shape為(Number of figures,Width,Height,Channel)。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_batch(fpath):\n", "    \"\"\"This function extract a batch of CIFAR10 data\n", "    from the chosen binary file.\n", "    This function is a simplified version of\n", "    https://github.com/keras-team/keras/blob/master/keras/datasets/cifar.py\n", "    \"\"\"\n", "    with open(fpath, \"rb\") as f:\n", "        d = pickle.load(f, encoding=\"bytes\")\n", "        # Keys are in the \"byte\" format. Let's decode them into utf8 strings.\n", "        d_decoded = {}\n", "        for k, v in d.items():\n", "            d_decoded[k.decode(\"utf8\")] = v\n", "        d = d_decoded\n", "    data = d[\"data\"]\n", "    labels = d[\"labels\"]\n", "    data = data.reshape(data.shape[0], 3, 32, 32)\n", "    data = data.transpose(0, 2, 3, 1)\n", "    return data, labels\n", "\n", "\n", "def load_data(path):\n", "    \"\"\"\n", "    載入以binary方式儲存的影像至電腦內記憶體。\n", "    \"\"\"\n", "    num_train_samples = 50000\n", "\n", "    x_train = np.zeros((num_train_samples, 32, 32, 3), dtype=\"uint8\")\n", "    y_train = np.zeros((num_train_samples,), dtype=\"uint8\")\n", "\n", "    for i in range(1, 6):\n", "        fpath = os.path.join(path, \"data_batch_\") + str(i)\n", "        data, labels = load_batch(fpath)\n", "        x_train[(i - 1) * 10000 : i * 10000, :, :, :] = data\n", "        y_train[(i - 1) * 10000 : i * 10000] = labels\n", "\n", "    fpath = os.path.join(path, \"test_batch\")\n", "    x_test, y_test = load_batch(fpath)\n", "\n", "    return (x_train, y_train), (np.array(x_test), np.array(y_test, dtype=\"uint8\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(x_train, y_train), (x_test, y_test) = load_data(\"../datasets/cifar-10-batches-py\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(x_train.shape, y_train.shape)\n", "print(x_test.shape, y_test.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以上，我們得到了x_train, x_test, y_train,y_test四個放置圖片的矩陣，其shape均為(Number of figures,Width,Height,Channel)。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接著，我們抽出幾張圖來看，稍微了解一下這些資料大概的樣貌："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"../datasets/cifar-10-batches-py/labels.txt\") as reader:\n", "    fig_labels = reader.read()\n", "fig_labels = fig_labels.split(\"\\n\")[:-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["idx_to_label = {}\n", "for idx, fig_labels in enumerate(fig_labels):\n", "    idx_to_label[idx] = fig_labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 隨機抽取12張圖來看一下\n", "num_figures_display = 12\n", "fig_indexes = np.random.choice(x_train.shape[0], num_figures_display)\n", "\n", "fig, axes = plt.subplots(2, 6)\n", "for fig_idx, axis in zip(fig_indexes, axes.reshape(-1)):\n", "    axis.axis(\"off\")\n", "    axis.imshow(x_train[fig_idx])\n", "    axis.set_title(idx_to_label[y_train[fig_idx]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#00)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='02'> 將圖片做resize以及normalization </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "from keras.utils import to_categorical\n", "\n", "# 做normalization。\n", "# 一個簡單的方式，是將x直接除以255，使得x內的所有值均分佈於[0,1]之間。\n", "x_train = x_train / 255.0\n", "x_test = x_test / 255.0\n", "\n", "# 調整x_train每張圖的大小從(32,32)乘以三倍，變成(96,96)。\n", "x_train_resized = np.zeros((50000, 96, 96, 3), dtype=np.float32)\n", "for idx, img in enumerate(x_train):\n", "    if idx % 10000 == 0:\n", "        print(idx)\n", "    x_train_resized[idx, :] = cv2.resize(\n", "        img, None, fx=3, fy=3, interpolation=cv2.INTER_AREA\n", "    )\n", "\n", "# 調整x_test每張圖的大小從(32,32)乘以三倍，變成(96,96)。\n", "x_test_resized = np.zeros((10000, 96, 96, 3), dtype=np.float32)\n", "for idx, img in enumerate(x_test):\n", "    if idx % 1000 == 0:\n", "        print(idx)\n", "    x_test_resized[idx, :] = cv2.resize(\n", "        img, None, fx=3, fy=3, interpolation=cv2.INTER_AREA\n", "    )\n", "\n", "# 將y轉換成為one hot的形式\n", "y_train_one_hot = to_categorical(y_train, num_classes=10)\n", "y_test_one_hot = to_categorical(y_test, num_classes=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#00)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='03'> 建造```conv block``` 並疊加成為CNN模型 </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ConvBlock(tf.keras.layers.Layer):\n", "    \"\"\"Class for the `BN-ReLU-Conv` block.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        filters,\n", "        kernel_size=3,\n", "        strides=1,\n", "        use_bias=False,\n", "        axis=-1,\n", "        epsilon=1e-3,\n", "        l2_strength=1.0e-5,\n", "        **kwargs\n", "    ):\n", "        \"\"\"Initialize all the necessary ingredients of a `Conv` block.\n", "\n", "        Args:\n", "            filters: Integer, number of filters of the Conv layer.\n", "            kernel_size: Integer, kernel size of the Conv layer.\n", "            strides: Integer, number of strides of the Conv layer.\n", "            use_bias: Boolean, if `True`, the constructed Conv layer will have a trainable bias.\n", "            axis: Int, if `axis=-1 or 3`, the input tensor has the format of (N,H,W,C);\n", "             if `axis=1 or -3`, the input tensor has the format of (N,C,H,W).\n", "        \"\"\"\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "\n", "        if axis == -1 or axis == 3:\n", "            data_format = \"channels_last\"\n", "        elif axis == 1 or axis == -3:\n", "            data_format = \"channels_firt\"\n", "        else:\n", "            raise ValueError(\"Data format invalid.\")\n", "\n", "        self.conv = tf.keras.layers.Conv2D(\n", "            filters,\n", "            kernel_size=kernel_size,\n", "            strides=strides,\n", "            use_bias=use_bias,\n", "            data_format=data_format,\n", "            kernel_regularizer=tf.keras.regularizers.l2(l2_strength),\n", "            padding=\"SAME\",\n", "        )\n", "\n", "        norm_params = {\"epsilon\": epsilon, \"axis\": axis}\n", "        self.bn = tf.keras.layers.BatchNormalization(**norm_params)\n", "\n", "    def build(self, input_shape):\n", "        built = True\n", "\n", "    def compute_output_shape(self, input_shape):\n", "        return input_shape\n", "\n", "    def call(self, x, training=None):\n", "        return self.conv(tf.nn.relu(self.bn(x, training=training)))\n", "\n", "\n", "# 測試: 正向傳遞 (forward propagation)\n", "fake_data = np.random.normal(0, 1, (5, 32, 32, 3)).astype(np.float32)\n", "cv_blk = ConvBlock(filters=128)\n", "print(cv_blk(fake_data).shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得模型\n", "model = tf.keras.models.Sequential()\n", "model.add(ConvBlock(filters=32, input_shape=(32, 32, 3)))\n", "model.add(ConvBlock(filters=32))\n", "model.add(ConvBlock(filters=64))\n", "model.add(ConvBlock(filters=64))\n", "model.add(tf.keras.layers.Flatten())\n", "model.add(tf.keras.layers.Dense(10, activation=\"softmax\"))\n", "\n", "# 檢視模型摘要\n", "model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 編譯模型：給定模型目標和訓練方式。\n", "model.compile(loss=\"categorical_crossentropy\", optimizer=\"Adam\", metrics=[\"accuracy\"])\n", "\n", "# 訓練模型\n", "history = model.fit(\n", "    x=x_train,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test, y_test_one_hot),\n", "    epochs=10,\n", "    batch_size=128,\n", ")\n", "\n", "# 畫出訓練過程\n", "plt.plot(history.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(history.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#00)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='04'> 建造```residual block``` 並疊加成為ResNet </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Residual(tf.keras.layers.Layer):\n", "    \"\"\"Class for the building block of ResNet V2.\n", "\n", "    Reference:\n", "      \"Identity Mappings in Deep Residual Networks\"; https://arxiv.org/abs/1603.05027\n", "    Code in principle follows:\n", "      https://github.com/apache/incubator-mxnet/blob/master/python/mxnet/gluon/model_zoo/vision/resnet.py\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        filters,\n", "        downsample=False,\n", "        strides=1,\n", "        use_bias=True,\n", "        epsilon=1e-3,\n", "        axis=-1,\n", "        l2_strength=1.0e-5,\n", "        **kwargs\n", "    ):\n", "        \"\"\"Initialize all the necessary layers and parameters for the `Residual` block.\n", "\n", "        Args:\n", "            filters: Number of filters of all the used Conv layers.\n", "            downsample: If to reduce size and/or channels of the input feature map.\n", "            strides: Strides of the first Conv layers. If >1, the input feature map will be down-sampled.\n", "            use_bias: If `True`, all the Conv layers will have trainable biases.\n", "        \"\"\"\n", "        super(Residual, self).__init__(**kwargs)\n", "\n", "        if axis == -1 or axis == 3:\n", "            data_format = \"channels_last\"\n", "        elif axis == 1 or axis == -3:\n", "            data_format = \"channels_firt\"\n", "        else:\n", "            raise ValueError(\"Data format invalid.\")\n", "\n", "        self.conv1 = tf.keras.layers.Conv2D(\n", "            filters,\n", "            kernel_size=3,\n", "            strides=strides,\n", "            use_bias=use_bias,\n", "            data_format=data_format,\n", "            kernel_regularizer=tf.keras.regularizers.l2(l2_strength),\n", "            padding=\"SAME\",\n", "        )\n", "\n", "        self.conv2 = tf.keras.layers.Conv2D(\n", "            filters,\n", "            kernel_size=3,\n", "            strides=1,\n", "            use_bias=use_bias,\n", "            data_format=data_format,\n", "            kernel_regularizer=tf.keras.regularizers.l2(l2_strength),\n", "            padding=\"SAME\",\n", "        )\n", "\n", "        self.downsample = downsample\n", "        if self.downsample:\n", "            self.conv_down = tf.keras.layers.Conv2D(\n", "                filters,\n", "                kernel_size=1,\n", "                strides=strides,\n", "                use_bias=use_bias,\n", "                data_format=data_format,\n", "                kernel_regularizer=tf.keras.regularizers.l2(l2_strength),\n", "                padding=\"SAME\",\n", "            )\n", "\n", "        norm_params = {\"epsilon\": epsilon, \"axis\": axis}\n", "        self.norm1 = tf.keras.layers.BatchNormalization(**norm_params)\n", "        self.norm2 = tf.keras.layers.BatchNormalization(**norm_params)\n", "\n", "    def call(self, x, training=None):\n", "        # 實作練習\n", "        # ...\n", "        # ...\n", "        # ...\n", "\n", "        return x + residual"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 測試: 正向傳遞 (forward propagation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 維持張量大小\n", "# input tensor shape = (N,H,W,C) = output tensor shape\n", "\n", "fake_data = np.random.normal(0, 1, (5, 32, 32, 128)).astype(np.float32)\n", "res_blk = Residual(filters=128)\n", "print(res_blk(fake_data).shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 張量通道數減半\n", "# input tensor shape = (N,H,W,C)\n", "# output tensor shape = (N,H,W,C/2)\n", "\n", "fake_data = np.random.normal(0, 1, (5, 32, 32, 128)).astype(np.float32)\n", "res_blk = Residual(filters=64, downsample=True)\n", "print(res_blk(fake_data).shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 張量長寬各減半\n", "# input tensor shape = (N,H,W,C)\n", "# output tensor shape = (N,H/2,W/2,C)\n", "\n", "fake_data = np.random.normal(0, 1, (5, 32, 32, 128)).astype(np.float32)\n", "res_blk = Residual(filters=128, downsample=True, strides=2)\n", "print(res_blk(fake_data).shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 開始建立和訓練模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得模型\n", "model = tf.keras.models.Sequential()\n", "model.add(ConvBlock(filters=32, input_shape=(32, 32, 3)))\n", "model.add(Residual(filters=32))\n", "model.add(Residual(filters=32))\n", "model.add(Residual(filters=64, downsample=True, strides=2))\n", "model.add(Residual(filters=64))\n", "model.add(tf.keras.layers.Flatten())\n", "model.add(tf.keras.layers.Dense(10, activation=\"softmax\"))\n", "\n", "# 檢視模型摘要\n", "model.summary()\n", "\n", "# 編譯模型：給定模型目標和訓練方式。\n", "model.compile(loss=\"categorical_crossentropy\", optimizer=\"Adam\", metrics=[\"accuracy\"])\n", "\n", "# 訓練模型\n", "history = model.fit(\n", "    x=x_train,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test, y_test_one_hot),\n", "    epochs=10,\n", "    batch_size=128,\n", ")\n", "\n", "# 畫出訓練過程\n", "plt.plot(history.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(history.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#00)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='05'> 微調```residual block```成為```dense block```，並建立出DenseNet </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# class DenseBlock(...):\n", "#     ...\n", "#     ...\n", "#     ..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得模型\n", "model = tf.keras.models.Sequential()\n", "model.add(ConvBlock(filters=32, input_shape=(32, 32, 3)))\n", "model.add(<PERSON><PERSON><PERSON><PERSON>(filters=32))\n", "model.add(<PERSON><PERSON><PERSON><PERSON>(filters=32))\n", "model.add(<PERSON><PERSON><PERSON><PERSON>(filters=32))\n", "model.add(<PERSON><PERSON><PERSON><PERSON>(filters=32))\n", "model.add(tf.keras.layers.Flatten())\n", "model.add(tf.keras.layers.Dense(10, activation=\"softmax\"))\n", "\n", "# 檢視模型摘要\n", "model.summary()\n", "\n", "# 編譯模型：給定模型目標和訓練方式。\n", "model.compile(loss=\"categorical_crossentropy\", optimizer=\"Adam\", metrics=[\"accuracy\"])\n", "\n", "# 訓練模型\n", "history = model.fit(\n", "    x=x_train,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test, y_test_one_hot),\n", "    epochs=10,\n", "    batch_size=128,\n", ")\n", "\n", "# 畫出訓練過程\n", "plt.plot(history.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(history.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#00)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}