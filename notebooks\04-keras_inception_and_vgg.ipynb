{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 本筆記將實作一些Keras API的建模方式。我們練習的資料集為CIFAR10。\n", "\n", "\n", "#### 核心概念: \n", "\n", "* 知道如何使用```Model``` API來建模。\n", "* 學習將基本網路結構模組化(例: ```conv block```, ```inception block```)，方便日後將模組拿來重新利用。\n", "* 了解如何複用別人寫好的模型來建模。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CIFAR10資料集：https://www.cs.toronto.edu/~kriz/cifar.html"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 本筆記的內容如下："]}, {"cell_type": "markdown", "metadata": {}, "source": ["* [載入圖片至電腦記憶體](#01)\n", "* [將圖片做resize以及normalization](#02)\n", "* [以```Sequential()```逐層疊加出CNN模型](#03)\n", "* [評估模型好壞](#031)\n", "* [以```Model(input,output)```建構出CNN模型](#04)\n", "* [將CNN模型模組化](#05)\n", "* [將MY_CNN網路內的conv block換成inception block](#06)\n", "* [拿Keras內建的VGG16模型架構來建立模型](#07)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "os.environ[\"KERAS_BACKEND\"] = \"torch\"\n", "\n", "import keras\n", "\n", "keras.config.backend()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.set()\n", "import pandas as pd\n", "\n", "from sklearn.metrics import classification_report\n", "import json\n", "import pickle"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='01'>載入圖片至電腦記憶體 </a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["首先看一下包含資料集的資料夾有什麼內容："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! ls -hl ../datasets/cifar-10-batches-py/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["data_batch_1, data_batch_2,..data_batch_5以及test_batch是以binary的方式儲存在硬碟裡。以下我們寫幾個函數，用以載入這些binary格式的圖檔至電腦內的記憶體中，並且將圖的以矩陣的方式儲存。這些圖矩陣的shape為(Number of figures,Width,Height,Channel)。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_batch(fpath):\n", "    \"\"\"This function extract a batch of CIFAR10 data\n", "    from the chosen binary file.\n", "    This function is a simplified version of\n", "    https://github.com/keras-team/keras/blob/master/keras/datasets/cifar.py\n", "    \"\"\"\n", "    with open(fpath, \"rb\") as f:\n", "        d = pickle.load(f, encoding=\"bytes\")\n", "        # Keys are in the \"byte\" format. Let's decode them into utf8 strings.\n", "        d_decoded = {}\n", "        for k, v in d.items():\n", "            d_decoded[k.decode(\"utf8\")] = v\n", "        d = d_decoded\n", "    data = d[\"data\"]\n", "    labels = d[\"labels\"]\n", "    data = data.reshape(data.shape[0], 3, 32, 32)\n", "    data = data.transpose(0, 2, 3, 1)\n", "    return data, labels\n", "\n", "\n", "def load_data(path):\n", "    \"\"\"\n", "    載入以binary方式儲存的影像至電腦內記憶體。\n", "    \"\"\"\n", "    num_train_samples = 50000\n", "\n", "    x_train = np.zeros((num_train_samples, 32, 32, 3), dtype=\"uint8\")\n", "    y_train = np.zeros((num_train_samples,), dtype=\"uint8\")\n", "\n", "    for i in range(1, 6):\n", "        fpath = os.path.join(path, \"data_batch_\") + str(i)\n", "        data, labels = load_batch(fpath)\n", "        x_train[(i - 1) * 10000 : i * 10000, :, :, :] = data\n", "        y_train[(i - 1) * 10000 : i * 10000] = labels\n", "\n", "    fpath = os.path.join(path, \"test_batch\")\n", "    x_test, y_test = load_batch(fpath)\n", "\n", "    return (x_train, y_train), (np.array(x_test), np.array(y_test, dtype=\"uint8\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(x_train, y_train), (x_test, y_test) = load_data(\"../datasets/cifar-10-batches-py\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(x_train.shape, y_train.shape)\n", "print(x_test.shape, y_test.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以上，我們得到了x_train, x_test, y_train,y_test四個放置圖片的矩陣，其shape均為(Number of figures,Width,Height,Channel)。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接著，我們抽出幾張圖來看，稍微了解一下這些資料大概的樣貌："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"../datasets/cifar-10-batches-py/labels.txt\") as reader:\n", "    fig_labels = reader.read()\n", "fig_labels = fig_labels.split(\"\\n\")[:-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["idx_to_label = {}\n", "for idx, fig_labels in enumerate(fig_labels):\n", "    idx_to_label[idx] = fig_labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 隨機抽取12張圖來看一下\n", "num_figures_display = 12\n", "fig_indexes = np.random.choice(x_train.shape[0], num_figures_display)\n", "\n", "fig, axes = plt.subplots(2, 6)\n", "for fig_idx, axis in zip(fig_indexes, axes.reshape(-1)):\n", "    axis.axis(\"off\")\n", "    axis.imshow(x_train[fig_idx])\n", "    axis.set_title(idx_to_label[y_train[fig_idx]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='02'> 將圖片做resize以及normalization </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "from keras.utils import to_categorical\n", "\n", "# 做normalization。\n", "# 一個簡單的方式，是將x直接除以255，使得x內的所有值均分佈於[0,1]之間。\n", "x_train = x_train / 255.0\n", "x_test = x_test / 255.0\n", "\n", "# 調整x_train每張圖的大小從(32,32)乘以三倍，變成(96,96)。\n", "x_train_resized = np.zeros((50000, 96, 96, 3), dtype=np.float32)\n", "for idx, img in enumerate(x_train):\n", "    if idx % 10000 == 0:\n", "        print(idx)\n", "    x_train_resized[idx, :] = cv2.resize(\n", "        img, None, fx=3, fy=3, interpolation=cv2.INTER_AREA\n", "    )\n", "\n", "# 調整x_test每張圖的大小從(32,32)乘以三倍，變成(96,96)。\n", "x_test_resized = np.zeros((10000, 96, 96, 3), dtype=np.float32)\n", "for idx, img in enumerate(x_test):\n", "    if idx % 1000 == 0:\n", "        print(idx)\n", "    x_test_resized[idx, :] = cv2.resize(\n", "        img, None, fx=3, fy=3, interpolation=cv2.INTER_AREA\n", "    )\n", "\n", "# 將y轉換成為one hot的形式\n", "y_train_one_hot = to_categorical(y_train, num_classes=10)\n", "y_test_one_hot = to_categorical(y_test, num_classes=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='03'> 以```Sequential()```逐層疊加出CNN模型 </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Sequential\n", "from keras.layers import (\n", "    <PERSON><PERSON>,\n", "    Conv2D,\n", "    MaxPooling2D,\n", "    <PERSON><PERSON>,\n", "    Dropout,\n", "    Activation,\n", ")\n", "\n", "\n", "class MY_CNN:\n", "    \"\"\"建立一個CNN模型\"\"\"\n", "\n", "    def __init__(self, in_shape, out_classes):\n", "        self.in_shape = in_shape\n", "        self.out_classes = out_classes\n", "\n", "    def build_model(self):\n", "        \"\"\"以Sequential()逐層疊加模型。\"\"\"\n", "\n", "        model = Sequential()\n", "        # conv block 1\n", "        model.add(\n", "            Conv2D(\n", "                32, (3, 3), padding=\"same\", activation=\"relu\", input_shape=self.in_shape\n", "            )\n", "        )\n", "        model.add(Conv2D(32, (3, 3), activation=\"relu\"))\n", "        model.add(MaxPooling2D(pool_size=(2, 2)))\n", "        model.add(Dropout(0.25))\n", "        # conv block 2\n", "        model.add(Conv2D(64, (3, 3), padding=\"same\", activation=\"relu\"))\n", "        model.add(Conv2D(64, (3, 3), activation=\"relu\"))\n", "        model.add(MaxPooling2D(pool_size=(2, 2)))\n", "        model.add(Dropout(0.25))\n", "        # dense block\n", "        model.add(<PERSON><PERSON>())\n", "        model.add(<PERSON><PERSON>(512, activation=\"relu\"))\n", "        model.add(Dropout(0.5))\n", "        model.add(<PERSON><PERSON>(self.out_classes))\n", "        model.add(Activation(\"softmax\"))\n", "        return model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得模型\n", "model = MY_CNN(in_shape=(32, 32, 3), out_classes=10).build_model()\n", "# 編譯模型：給定模型目標和訓練方式。\n", "model.compile(loss=\"categorical_crossentropy\", optimizer=\"Adam\", metrics=[\"accuracy\"])\n", "# 訓練模型\n", "history = model.fit(\n", "    x=x_train,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test, y_test_one_hot),\n", "    epochs=20,\n", "    batch_size=128,\n", ")\n", "# 畫出訓練過程\n", "plt.plot(history.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(history.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <a id='031'> 評估模型好壞 </a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看metrics，如f1, precision, recall的表現:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import classification_report\n", "\n", "predY = model.predict(x_test).argmax(axis=1)\n", "print(classification_report(y_test, predY))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看accuracy的表現:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 練習於此\n", "# 提示：使用model.predict(...)或model.evaluate(...)\n", "#"]}, {"cell_type": "markdown", "metadata": {}, "source": ["隨機抽取12張圖來看一下:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_figures_display = 12\n", "fig_indexes = np.random.choice(x_test.shape[0], num_figures_display)\n", "\n", "# 建立 2X6 子圖\n", "fig, axes = plt.subplots(2, 6, figsize=(10, 5))\n", "for fig_idx, axis in zip(fig_indexes, axes.reshape(-1)):\n", "    axis.axis(\"off\")\n", "    axis.imshow(x_test[fig_idx])  # 畫圖\n", "    label = idx_to_label[y_test[fig_idx]]  # 取得真實標籤\n", "    pred_label = idx_to_label[predY[fig_idx]]  # 取得預測標籤\n", "\n", "    # 畫出真實標籤和預測標籤\n", "    if label != pred_label:\n", "        axis.text(16, -3, pred_label, fontsize=\"large\", color=\"r\", ha=\"center\")\n", "    else:\n", "        axis.text(16, -3, pred_label, fontsize=\"large\", color=\"b\", ha=\"center\")\n", "    axis.set_title(label + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='04'>以```Model(input,output)```建構出CNN模型 </a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Model\n", "from keras.layers import (\n", "    <PERSON><PERSON>,\n", "    Conv2D,\n", "    MaxPooling2D,\n", "    <PERSON><PERSON>,\n", "    Dropout,\n", "    Activation,\n", "    Input,\n", ")\n", "\n", "\n", "class MY_CNN:\n", "    \"\"\"建立一個CNN模型。\"\"\"\n", "\n", "    def __init__(self, in_shape, out_classes):\n", "        self.in_shape = in_shape\n", "        self.out_classes = out_classes\n", "\n", "    def build_model(self):\n", "        \"\"\"以Sequential()逐層疊加模型。\"\"\"\n", "        image = Input(self.in_shape)\n", "        # conv block 1\n", "        conv1 = Conv2D(32, (3, 3), padding=\"same\", activation=\"relu\")\n", "        conv2 = Conv2D(32, (3, 3), activation=\"relu\")\n", "        pool1 = MaxPooling2D(pool_size=(2, 2))\n", "        dropout = Dropout(0.25)\n", "\n", "        x = dropout(pool1(conv2(conv1(image))))\n", "        # conv block 2\n", "        conv1 = Conv2D(64, (3, 3), padding=\"same\", activation=\"relu\")\n", "        conv2 = Conv2D(64, (3, 3), activation=\"relu\")\n", "        pool1 = MaxPooling2D(pool_size=(2, 2))\n", "        dropout = Dropout(0.25)\n", "        x = dropout(pool1(conv2(conv1(x))))\n", "        # dense block\n", "        x = Dense(self.out_classes, activation=\"softmax\")(\n", "            Dropout(0.5)(<PERSON><PERSON>(512, activation=\"relu\")(<PERSON><PERSON>()(x)))\n", "        )\n", "        model = Model(image, x)\n", "        return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='05'> 將CNN模型模組化</a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Model\n", "from keras.layers import (\n", "    <PERSON><PERSON>,\n", "    Conv2D,\n", "    MaxPooling2D,\n", "    <PERSON><PERSON>,\n", "    Dropout,\n", "    Activation,\n", "    Input,\n", ")\n", "\n", "\n", "class MY_CNN:\n", "    \"\"\"建立一個CNN模型。\"\"\"\n", "\n", "    def __init__(self, in_shape, out_classes):\n", "        self.in_shape = in_shape\n", "        self.out_classes = out_classes\n", "\n", "    def conv_block(self, n, x):\n", "        \"\"\"建構convolution block。\"\"\"\n", "        conv1 = Conv2D(n, (3, 3), padding=\"same\", activation=\"relu\")\n", "        conv2 = Conv2D(n, (3, 3), activation=\"relu\")\n", "        pool1 = MaxPooling2D(pool_size=(2, 2))\n", "        dropout = Dropout(0.25)\n", "        x = dropout(pool1(conv2(conv1(x))))\n", "        return x\n", "\n", "    def build_model(self):\n", "        \"\"\"以Model(input,output)的方式建立模型。\"\"\"\n", "\n", "        image = Input(self.in_shape)  # input image\n", "        x = self.conv_block(32, image)  # conv block 1\n", "        x = self.conv_block(64, x)  # conv block 2\n", "\n", "        # dense block\n", "        x = Dense(self.out_classes, activation=\"softmax\")(\n", "            Dropout(0.5)(<PERSON><PERSON>(512, activation=\"relu\")(<PERSON><PERSON>()(x)))\n", "        )\n", "        # Build the model out of the graph, given tne input/output tensor\n", "        model = Model(image, x)\n", "        return model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得模型\n", "model = MY_CNN(in_shape=(32, 32, 3), out_classes=10).build_model()\n", "# 編譯模型：給定模型目標和訓練方式。\n", "model.compile(loss=\"categorical_crossentropy\", optimizer=\"Adam\", metrics=[\"accuracy\"])\n", "# 訓練模型\n", "history = model.fit(\n", "    x=x_train,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test, y_test_one_hot),\n", "    epochs=20,\n", "    batch_size=128,\n", ")\n", "# 畫出訓練過程\n", "plt.plot(history.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(history.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以下我們嘗試將一個cnn block換成inception block (GoogleNet內部的主要架構)，看訓練模型會有什麼樣的結果。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='06'>將MY_CNN內的一個conv block換成inception block</a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Model\n", "from keras.layers import (\n", "    <PERSON><PERSON>,\n", "    Conv2D,\n", "    MaxPooling2D,\n", "    <PERSON><PERSON>,\n", "    Dropout,\n", "    Activation,\n", "    Input,\n", "    Concatenate,\n", ")\n", "\n", "\n", "class MY_CNN_INCEPT_BLOCK:\n", "    \"\"\"建立一個CNN模型\"\"\"\n", "\n", "    def __init__(self, in_shape, out_classes):\n", "        self.in_shape = in_shape\n", "        self.out_classes = out_classes\n", "\n", "    def conv_block(self, n, x):\n", "        conv1 = Conv2D(n, (3, 3), padding=\"same\", activation=\"relu\")\n", "        conv2 = Conv2D(n, (3, 3), activation=\"relu\")\n", "        pool1 = MaxPooling2D(pool_size=(2, 2))\n", "        dropout = Dropout(0.25)\n", "        x = dropout(pool1(conv2(conv1(x))))\n", "        return x\n", "\n", "    def inception_block(self, x, n1_1, n2_1, n2_3, n3_1, n3_5, n4_1):\n", "        path1_conv_1 = Conv2D(n1_1, (1, 1), activation=\"relu\")\n", "        path2_conv_1 = Conv2D(n2_1, (1, 1), activation=\"relu\")\n", "        path2_conv_3 = Conv2D(n2_3, (3, 3), padding=\"same\", activation=\"relu\")\n", "        path3_conv_1 = Conv2D(n3_1, (1, 1), activation=\"relu\")\n", "        path3_conv_5 = Conv2D(n3_5, (5, 5), padding=\"same\", activation=\"relu\")\n", "        path4_pool_3 = MaxPooling2D(pool_size=(3, 3), strides=(1, 1), padding=\"same\")\n", "        path4_conv_1 = Conv2D(n4_1, (1, 1), activation=\"relu\")\n", "\n", "        path1 = path1_conv_1(x)\n", "        path2 = path2_conv_3(path2_conv_1(x))\n", "        path3 = path3_conv_5(path3_conv_1(x))\n", "        path4 = path4_conv_1(path4_pool_3(x))\n", "        return Concatenate(axis=-1)([path1, path2, path3, path4])\n", "\n", "    def build_model(self):\n", "        \"\"\"以Model(input,output)的方式建立模型。\"\"\"\n", "\n", "        image = Input(self.in_shape)  # input image\n", "        x = self.conv_block(32, image)  # conv block\n", "        x = self.inception_block(x, 8, 12, 16, 2, 4, 4)  # inception block\n", "        # entering into the dense block\n", "        x = Dense(self.out_classes, activation=\"softmax\")(\n", "            Dropout(0.5)(<PERSON><PERSON>(512, activation=\"relu\")(<PERSON><PERSON>()(x)))\n", "        )\n", "        # Build the model out of the graph, given tne input/output tensor\n", "        model = Model(image, x)\n", "        return model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得模型\n", "model = MY_CNN_INCEPT_BLOCK(in_shape=(32, 32, 3), out_classes=10).build_model()\n", "# 編譯模型：給定模型目標和訓練方式。\n", "model.compile(loss=\"categorical_crossentropy\", optimizer=\"Adam\", metrics=[\"accuracy\"])\n", "# 訓練模型\n", "history = model.fit(\n", "    x=x_train,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test, y_test_one_hot),\n", "    epochs=20,\n", "    batch_size=128,\n", ")\n", "# 畫出訓練過程\n", "plt.plot(history.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(history.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <a id='07'>拿Keras內建的VGG16模型架構來建立模型</a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```keras.applications```裡面內建了一些常見的架構，如VGG, GoogleNet, ResNet等。以下我們嘗試來使用VGG16建立模型："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.applications import VGG16\n", "from keras import Model\n", "from keras.optimizers import SGD"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 取得內建的VGG模型 (去掉後層分類器的部分)\n", "model = VGG16(include_top=False, input_shape=(96, 96, 3))\n", "# 添加後層分類器至內建VGG模型\n", "last = model.output\n", "x = Flatten()(last)\n", "x = Dense(512, activation=\"relu\")(x)\n", "x = Dropout(0.5)(x)\n", "preds = Dense(10, activation=\"softmax\")(x)\n", "# 組合模型\n", "model = Model(model.input, preds)\n", "# 編譯模型，告知模型訓練方式\n", "model.compile(\n", "    loss=\"categorical_crossentropy\",\n", "    optimizer=SGD(lr=1e-2, decay=1e-6, momentum=0.9, nesterov=True),\n", "    metrics=[\"accuracy\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rec = model.fit(\n", "    x=x_train_resized,\n", "    y=y_train_one_hot,\n", "    validation_data=(x_test_resized, y_test_one_hot),\n", "    epochs=2,\n", "    batch_size=128,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # 畫出訓練過程\n", "# plt.plot(rec.history['accuracy'], ms=5, marker='o', label='accuracy')\n", "# plt.plot(rec.history['val_accuracy'], ms=5, marker='o', label='val accuracy')\n", "# plt.legend()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#%E6%9C%AC%E7%AD%86%E8%A8%98%E7%9A%84%E5%85%A7%E5%AE%B9%E5%A6%82%E4%B8%8B%EF%BC%9A)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}