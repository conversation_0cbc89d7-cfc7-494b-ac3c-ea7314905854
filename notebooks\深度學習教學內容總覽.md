# 深度學習教學內容總覽

### 免費深度學習線上資源 https://zh.d2l.ai/
### https://space.bilibili.com/1567748478/lists?cid=175509

## 📚 課程架構

### PyTorch 基礎系列

#### 00-torch_gradient_descent.ipynb
**學習目標：** 初步理解 PyTorch 倒數計算和模型優化
- 基本梯度計算練習
- 自動微分機制
- 梯度下降優化器使用
- 數學函數微分實作

#### 01-torch_linear.ipynb  
**學習目標：** Linear Layer 操作與多層感知機建立
- Linear Layer 輸入/輸出維度理解
- Logistic Regression 實作
- Softmax Regression 實作
- Multilayer Perceptron 建立與訓練

### Keras 基礎與進階系列

#### 02-keras_intro_linear_conv_pooling.ipynb
**學習目標：** Keras 基礎層級操作
- Dense Layer 輸入/輸出測試
- Multilayer Perceptron 建立
- Convolution/Pooling Layer 維度理解
- 基礎神經網路架構

#### 03-keras_mnist_dataset_modeling.ipynb
**學習目標：** 完整建模流程實戰 (MNIST 手寫數字)
- **資料準備**
  - 圖片載入與路徑管理
  - 資料分割 (train/validation/test)
  - 圖片數值化處理
- **Softmax Regression**
  - One-hot encoding
  - 模型建立與訓練
  - 準確率評估與視覺化
  - 模型儲存與載入
- **Simple CNN**
  - 卷積神經網路架構
  - 訓練結果分析
  - 分類報告生成

#### 04-keras_inception_and_vgg.ipynb
**學習目標：** 進階 CNN 架構實作 (CIFAR10)
- Model API 建模方式
- 網路結構模組化設計
- Inception Block 實作
- VGG16 預訓練模型應用
- 模型複用技巧

#### 05-keras_resnet_and_densenet.ipynb
**學習目標：** 現代深度網路架構 (CIFAR10)
- Subclassing API 建模
- ResNet 殘差連接實作
- DenseNet 密集連接實作
- 深度網路訓練技巧

## 🎯 學習路徑建議

1. **基礎概念** → `00-torch_gradient_descent.ipynb`
2. **線性模型** → `01-torch_linear.ipynb` 
3. **Keras 入門** → `02-keras_intro_linear_conv_pooling.ipynb`
4. **完整專案** → `03-keras_mnist_dataset_modeling.ipynb`
5. **進階架構** → `04-keras_inception_and_vgg.ipynb`
6. **現代網路** → `05-keras_resnet_and_densenet.ipynb`

## 📊 使用資料集

- **MNIST**: 手寫數字辨識 (28×28 灰階)
- **CIFAR10**: 彩色物件分類 (32×32 RGB)

## 🛠️ 技術重點

- **框架**: PyTorch, Keras
- **模型類型**: Linear, CNN, ResNet, DenseNet
- **技巧**: 資料預處理, 模型儲存, 視覺化, 模組化設計