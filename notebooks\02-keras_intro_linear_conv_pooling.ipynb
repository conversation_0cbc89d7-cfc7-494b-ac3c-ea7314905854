{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 本筆記目的：\n", "\n", "1. 理解Dense Layer的輸入/輸出資料大小。\n", "2. 能簡單的利用Dense Layer來建立並訓練Multi-layer perceptron。\n", "3. 理解Convolution/Pooling Layer的輸入/輸出資料大小。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### [A. 測試Dense Layer I/O, 並以Dense Layer建立模型](#A.-%E6%B8%AC%E8%A9%A6Dense-Layer-I/O,-%E4%B8%A6%E4%BB%A5Dense-Layer%E5%BB%BA%E7%AB%8B%E6%A8%A1%E5%9E%8B%E3%80%82)\n", "* [Sequential model: Logistic Regression](#Sequential-model:-Logistic-Regression)\n", "* [Sequential model: Softmax Regression](#Sequential-model:-Softmax-Regression)\n", "* [練習：建立Multilayer Perceptron模型，並且丟簡單資料進去做訓練](#%E7%B7%B4%E7%BF%92%EF%BC%9A%E5%BB%BA%E7%AB%8BMultilayer-Perceptron%E7%9A%84%E6%A8%A1%E5%9E%8B%EF%BC%8C%E4%B8%A6%E4%B8%94%E4%B8%9F%E4%BA%82%E6%95%B8%E8%B3%87%E6%96%99%E9%80%B2%E5%8E%BB%E5%81%9A%E8%A8%93%E7%B7%B4%E3%80%82)\n", "\n", "### [B. 測試Convolutional Layer I/O](#B.-%E6%B8%AC%E8%A9%A6Convolutional-Layer-I/O%E3%80%82)\n", "### [C. 測試Max Pooling Layer I/O](#C.-%E6%B8%AC%E8%A9%A6Max-Pooling-Layer-I/O%E3%80%82)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "os.environ[\"KERAS_BACKEND\"] = \"torch\"\n", "\n", "import keras\n", "\n", "keras.config.backend()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "import pandas as pd\n", "import sklearn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Sequential\n", "from keras.layers import Dense, Conv2D, MaxPooling2D\n", "from keras.optimizers import SGD"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A. 測試Dense Layer I/O, 並以Dense Layer建立模型。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential model: Logistic Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["randData = np.random.normal(0, 1, (5, 3))  # normal分佈的亂數資料當input,\n", "# 5個樣本，每個樣本有3個特徵\n", "\n", "model = Sequential()  # 定義模型\n", "model.add(Dense(1, input_shape=(3,), activation=\"sigmoid\"))\n", "# 模型加入只有一個神經元的Dense層，且用sigmoid啟用，輸出機率\n", "\n", "model.predict(randData)  # 應該會回傳5個介於0和1之間的數值。分別為各樣本的預測機率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回到頂部](#%E7%9B%AE%E6%A8%99%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential model: Softmax Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["randData = np.random.normal(\n", "    0, 1, (5, 3)\n", ")  # normal分佈的亂數資料當input, 5個樣本，每個樣本有3個特徵\n", "\n", "model = Sequential()\n", "model.add(\n", "    Dense(3, input_shape=(3,), activation=\"softmax\")\n", ")  # 定義3個神經元，並以Softmax啟用，輸出機率\n", "print(model.predict(randData))  # 往前傳遞\n", "# print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model.predict(randData).sum(axis=1))  # 驗證softmax輸出：P_A+P_B+P_C=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回到頂部](#%E7%9B%AE%E6%A8%99%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 練習：建立Multilayer Perceptron的模型，並且將$X_{new}$, $y_{new}$丟進去做訓練。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X = np.zeros((90, 3))\n", "for j in range(0, 30):\n", "    X[j, :] = 0.0\n", "for j in range(30, 60):\n", "    X[j, :] = 1.0\n", "for j in range(60, 90):\n", "    X[j, :] = 2.0\n", "y = X[:, 0]\n", "\n", "# # data standarization\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "scaler = StandardScaler()\n", "scaler = scaler.fit(X)\n", "X_new = scaler.transform(X)\n", "\n", "# # convert y into one-hot representation\n", "from keras.utils import to_categorical\n", "\n", "y_new = to_categorical(y)\n", "\n", "# print(\"X_new=\\n\",X_new)\n", "# print()\n", "# print(\"y_new=\\n\",y_new)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 練習於此\n", "# model=Sequential()\n", "# model.add(...)\n", "#"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 畫出訓練過程\n", "# plt.plot(hist.history['accuracy'],ms=5,marker='o',label='accuracy')\n", "# plt.legend()\n", "# plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回到頂部](#%E7%9B%AE%E6%A8%99%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## B. 測試Convolutional Layer I/O。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ```padding='valid'```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "padding = valid: $w^{'}=\\frac{W+2P-F}{S}+1$"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["randData = np.random.normal(\n", "    0, 1, (10, 5, 5, 3)\n", ")  # normal分佈的亂數資料當input, 10個3D樣本\n", "\n", "model = Sequential()\n", "model.add(\n", "    Conv2D(\n", "        filters=96,\n", "        kernel_size=(3, 3),\n", "        strides=(1, 1),\n", "        padding=\"valid\",\n", "        input_shape=(5, 5, 3),\n", "    )\n", ")\n", "print(model.predict(randData).shape)  # 看輸出資料的形狀"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ```padding='same'```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["padding = same: $ w'= ceil(w/s) $"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["randData = np.random.normal(\n", "    0, 1, (10, 20, 20, 3)\n", ")  # normal分佈的亂數資料當input, 10個3D樣本\n", "\n", "model = Sequential()\n", "model.add(\n", "    Conv2D(\n", "        filters=96,\n", "        kernel_size=(7, 7),\n", "        strides=(1, 1),\n", "        padding=\"same\",\n", "        input_shape=(20, 20, 3),\n", "    )\n", ")\n", "print(model.predict(randData).shape)  # 看輸出資料的形狀"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 若strides=(1,1)且```padding='same'```，則表示程式會利用padding來確保input data size和output data size相等。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reference:\n", "* https://cs231n.github.io/convolutional-networks/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回到頂部](#%E7%9B%AE%E6%A8%99%EF%BC%9A)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## C. 測試Max Pooling Layer I/O。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["randData = np.random.normal(\n", "    0, 1, (10, 4, 4, 3)\n", ")  # normal分佈的亂數資料當input, 10個3D樣本\n", "model = Sequential()\n", "model.add(MaxPooling2D(pool_size=(2, 2), strides=(2, 2), input_shape=(4, 4, 3)))\n", "\n", "print(model.predict(randData).shape)  # 看輸出資料的形狀"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回到頂部](#%E7%9B%AE%E6%A8%99%EF%BC%9A)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "torchv2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}