{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["末端層\n", "* 不追加函數作用在末端層 -> 末端層上面的每個神經元輸出值域[$-\\infty$, $+\\infty$]$ -> 處理回歸問題\n", "* 追加Sigmoid(二元分類)或Softmax(多元分類)函數作用在末端層 -> 末端層上面的每個神經元輸出值域[$0$, $1$]$ -> 處理分類問題"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 本筆記目的：\n", "\n", "1. 理解Linear Layer的輸入/輸出資料大小。\n", "2. 能簡單的利用Linear Layer來建立並訓練Multi-layer perceptron。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 測試Dense Layer I/O, 並以Dense Layer建立模型\n", "* Sequential model: Logistic Regression\n", "* Sequential model: Softmax Regression\n", "* 練習：建立Multilayer Perceptron模型，並且丟簡單資料進去做訓練"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "import pandas as pd\n", "import sklearn\n", "import os\n", "\n", "import torch\n", "\n", "sns.set()\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch.nn import Sequential\n", "from torch.nn import Linear, Conv2d, MaxPool2d\n", "from torch.nn import Sigmoid, Softmax, ReLU  # activation function\n", "\n", "from torch.optim import SGD\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "\n", "from torch.nn import CrossEntropyLoss"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A. 測試Dense Layer I/O, 並以Dense Layer建立模型。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential model: Logistic Regression"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["rand_data = np.random.normal(0, 1, (5, 3))  # [BS=5, num_features=3]\n", "rand_data = torch.Tensor(rand_data)  # 常態分佈的亂數資料當input;\n", "# 5個樣本，每個樣本有3個特徵"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["isinstance(torch.nn.<PERSON>(3, 1), torch.nn.<PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input shape= torch.Size([5, 3])\n", "tensor([[0.3231],\n", "        [0.2389],\n", "        [0.3234],\n", "        [0.3298],\n", "        [0.3060]], grad_fn=<SigmoidBackward0>)\n", "Output shape= torch.Size([5, 1])\n"]}], "source": ["# 定義模型\n", "print(f\"Input shape= {rand_data.shape}\")\n", "model = Sequential(\n", "    Linear(3, 1),  # [BS, 3] -> [BS, 1]\n", "    <PERSON>g<PERSON><PERSON>(),  # [BS, 1] -> [BS, 1]\n", ")  # 預測y=1的機率\n", "\n", "out = model(rand_data)\n", "print(out)\n", "print(f\"Output shape= {out.shape}\")\n", "# 應該會回傳5個小於1的數值。分別為各樣本的預測機率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential model: Softmax Regression"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [], "source": ["rand_data = np.random.normal(0, 1, (5, 3))  # [BS=5, num_features=3]\n", "rand_data = torch.Tensor(rand_data)  # 常態分佈的亂數資料當input,\n", "# 5個樣本，每個樣本有3個特徵"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1.0000, 1.0000, 1.0000, 1.0000, 1.0000], grad_fn=<SumBackward1>)"]}, "execution_count": 161, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定義模型\n", "model = Sequential(\n", "    Linear(3, 3),  # [BS=5, num_features=3] -> [BS=5, num_features=3]\n", "    Softmax(dim=-1),  # [BS=5, num_features=3] -> [BS=5, num_features=3]\n", ")\n", "\n", "model(rand_data).sum(\n", "    axis=-1\n", ")  # 驗證Softmax輸出：P_A+P_B+P_C=1 ([BS, num_probabilities=3] -> [BS,])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 練習：建立Multilayer Perceptron的模型，並且將$X_{new}$, $y_{new}$丟進去做訓練。"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [], "source": ["X = np.zeros((90, 3))\n", "for j in range(0, 30):\n", "    X[j, :] = 0.0\n", "for j in range(30, 60):\n", "    X[j, :] = 1.0\n", "for j in range(60, 90):\n", "    X[j, :] = 2.0\n", "\n", "y = X[:, 0].astype(np.int64)"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [], "source": ["# Data standarization\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "scaler = (\n", "    StandardScaler()\n", ")  # (1)估算每個資料欄位的mean, std (2)把N(mean, std^2)變成N(0, 1)\n", "# (2)怎麼做呢? X'= (X - mean) / std\n", "\n", "scaler = scaler.fit(X)\n", "X_new = scaler.transform(X)\n", "\n", "# One-hot encoding\n", "y_new = np.eye(3)[y]\n", "\n", "X_new = X_new.astype(np.float32)\n", "y_new = y_new.astype(np.float32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# === 建立模型 ===\n", "\n", "# 練習於此"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [], "source": ["assert isinstance(Sequential(), torch.nn.Mo<PERSON>)\n", "assert isinstance(torch.nn.<PERSON>(1, 1), torch.nn.<PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_step(\n", "    dataloader,\n", "    model: <PERSON>.nn.<PERSON>,\n", "    loss_fn,\n", "    optimizer,\n", "    verbose_every: int = 999,\n", ") -> None:\n", "    \"\"\"訓練一個epoch。\"\"\"\n", "    for iteration, (batch_x, batch_y) in enumerate(dataloader):\n", "\n", "        #########################\n", "        ## 練習: 添加正傳遞與倒傳遞\n", "        pred_y = model(batch_x)\n", "        loss_value = loss_fn(pred_y, batch_y)\n", "\n", "        # print(\n", "        #     f\"right before backward: {model[0]} has grad= {model[0].weight.grad is not None}\",\n", "        # )\n", "        # loss_value.backward()\n", "        # print(\n", "        #     f\"right after backward: {model[0]} has grad= {model[0].weight.grad is not None}\",\n", "        # )\n", "        #########################\n", "\n", "        if iteration + 1 % verbose_every == 0:\n", "            loss = loss_value.item()\n", "            print(\"loss={:.4f}\".format(iteration, loss))\n", "\n", "        optimizer.step()  # 梯度更新只要執行 step()即可。這個步驟會將每個權重更新為\n", "        # weight = weight - learning_rate * gradient\n", "        optimizer.zero_grad()\n", "\n", "\n", "\n", "def test_step(\n", "    dataloader,\n", "    model,\n", "    loss_fn,\n", "):\n", "    \"\"\"結束一個epoch的訓練後，測試模型表現。\"\"\"\n", "    size = len(dataloader.dataset)\n", "    test_loss, correct = 0, 0\n", "\n", "    with torch.no_grad():\n", "        for iteration, (batch_x, batch_y) in enumerate(dataloader):\n", "            pred_y = model(batch_x)\n", "\n", "            test_loss += loss_fn(pred_y, batch_y).item()\n", "            correct += (pred_y.argmax(axis=1) == batch_y).type(torch.float).sum().item()\n", "\n", "    test_loss /= size\n", "    correct /= size\n", "\n", "    print(\"test_loss={:.4f}, accuracy={:.2f}\".format(test_loss, correct))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1854/1967182984.py:2: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.detach().clone() or sourceTensor.detach().clone().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  X_new, y_new = torch.tensor(X_new), torch.tensor(y_new)\n", "/tmp/ipykernel_1854/1967182984.py:3: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.detach().clone() or sourceTensor.detach().clone().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  y = torch.tensor(y).to(torch.int64)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=1.00\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0365, accuracy=0.67\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0365, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0365, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0365, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0364, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0364, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0363, accuracy=0.67\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0363, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0362, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0360, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0358, accuracy=1.00\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0354, accuracy=1.00\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0347, accuracy=1.00\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0332, accuracy=0.67\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0268, accuracy=0.67\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.1358, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0355, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0312, accuracy=0.67\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0230, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0377, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0374, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0371, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0370, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0369, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0368, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0367, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0367, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0367, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0367, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0367, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0367, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.67\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "right before backward: Linear(in_features=3, out_features=100, bias=True) has grad= False\n", "right after backward: Linear(in_features=3, out_features=100, bias=True) has grad= True\n", "test_loss=0.0366, accuracy=0.33\n"]}], "source": ["# 載入資料成為DataLoader\n", "X_new, y_new = torch.tensor(X_new), torch.tensor(y_new)\n", "y = torch.tensor(y).to(torch.int64)\n", "td = TensorDataset(X_new, y)\n", "dl = DataLoader(td, batch_size=32, shuffle=True)\n", "\n", "# model = Sequential(\n", "#     Linear(3, num_units),\n", "#     ReLU(),\n", "#     Linear(num_units, num_units),  # R_+30\n", "#     ReLU(),\n", "#     Linear(num_units, num_units),  # R_+15\n", "#     ReLU(),\n", "#     Linear(num_units, num_units),  # R_-45\n", "#     ReLU(),\n", "#     Linear(num_units, num_units),  # R_+10\n", "#     ReLU(),\n", "#     Linear(num_units, 3),\n", "#     # Softmax(),\n", "# )\n", "\n", "num_units: int = 100\n", "num_hidden_layers: int = 6\n", "ActivationClass: torch.nn.ReLU | torch.nn.Softmax = torch.nn.ReLU\n", "model = Sequential(\n", "    Linear(3, num_units),\n", "    ActivationClass(),\n", "    *[\n", "        Linear(num_units, num_units),\n", "        ActivationClass(),\n", "    ]\n", "    * num_hidden_layers,\n", "    Linear(num_units, 3),\n", ")\n", "\n", "# 申論題: 短而肥或長而瘦, 哪個建模策略比較好? (模型權重數量要差不多)\n", "\n", "# 宣告模型訓練設定\n", "num_epochs = 200\n", "learning_rate = 0.2\n", "\n", "# 定義優化器, Loss函數\n", "ce_loss = CrossEntropyLoss()\n", "opt = SGD(\n", "    model.parameters(),\n", "    lr=learning_rate,\n", ")\n", "\n", "# 訓練模型\n", "for j in range(num_epochs):\n", "    train_step(dl, model, ce_loss, opt)\n", "    test_step(dl, model, ce_loss)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert hasattr(model, \"__getitem__\")\n", "assert hasattr(model, \"__iter__\")"]}, {"cell_type": "code", "execution_count": 236, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.weight torch.Size([100, 3])\n", "0.bias torch.<PERSON><PERSON>([100])\n", "2.weight torch.Size([100, 100])\n", "2.bias torch.<PERSON><PERSON>([100])\n", "14.weight torch.<PERSON><PERSON>([3, 100])\n", "14.bias torch.<PERSON><PERSON>([3])\n"]}], "source": ["for name, param in model.named_parameters():\n", "    print(name, param.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 4}