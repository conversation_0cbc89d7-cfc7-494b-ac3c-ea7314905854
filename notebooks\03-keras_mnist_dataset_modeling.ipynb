import os

os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["KERAS_BACKEND"] = "torch"

import keras

keras.config.backend()

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sns.set()
import pandas as pd
import sklearn

import os
from pathlib import Path

def filePathsGen(rootPath):
    """此函數將rootPath資料夾目錄中的所有圖片路徑資訊儲存至一個清單內。"""
    paths = []
    dirs = []
    for dirPath, dirNames, fileNames in os.walk(rootPath):
        for fileName in fileNames:
            fullPath = os.path.join(dirPath, fileName)
            paths.append((int(dirPath[len(rootPath)]), fullPath))
        dirs.append(dirNames)
    return dirs, paths

# data_dir = Path("/home/<USER>/BDSE_DL_v2025.02/datasets/mnist")
# path_data = [
#     (int(path.parent.stem), path) for path in sorted(data_dir.glob("**/*.jpg"))
# ]
# dfPath = pd.DataFrame(
#     path_data,
#     columns=["class", "path"],
# )

!ls ../datasets/mnist

dirs, paths = filePathsGen("../datasets/mnist/")  # 載入圖片路徑

dfPath = pd.DataFrame(paths, columns=["class", "path"])  # 圖片路徑存成Pandas資料表
dfPath.head(3)  # 看資料表前3個row

# 依照class分群後，數各群的數量，並繪圖
dfCountPerClass = dfPath.groupby("class").count()
dfCountPerClass.rename(columns={"path": "amount of figures"}, inplace=True)
dfCountPerClass.plot(kind="bar", rot=0)
plt.show()

dfFrac = dfPath.sample(frac=0.1)  # 打亂一下path data

train = dfFrac.sample(frac=0.8)  # 將path data隨機取樣，80%的path data當train
test = dfFrac.drop(train.index)  # 20%的path data當test

trainVal = test.sample(frac=0.5)
test = test.drop(trainVal.index)

print("shape(all figures)=\t\t", dfPath.shape)
print("shape(fraction of figures)=\t", dfFrac.shape)
print("shape(train)=\t\t\t", train.shape)
print("shape(trainVal)=\t\t", trainVal.shape)
print("shape(test)=\t\t\t", test.shape)

# 隨便抓三張圖來看
for j in range(3):
    img = plt.imread(train["path"].iloc[j])
    plt.imshow(img, cmap="gray")
    plt.show()

def dataLoad(dfPath):
    paths = dfPath["path"].values
    x = np.zeros((len(paths), 28, 28), dtype=np.float32)  # [num_paths, H=28, W=28]
    for j in range(len(paths)):
        x[j, :, :] = plt.imread(paths[j]) / 255.0  # [num_paths, H, W]

    y = dfPath["class"].values
    return x, y

trainX, trainY = dataLoad(train)
trainValX, trainValY = dataLoad(trainVal)
testX, testY = dataLoad(test)

print("train:\t", trainX.shape, trainY.shape)
print("trainVal:", trainValX.shape, trainValY.shape)
print("test:\t", testX.shape, testY.shape)

testX[3].shape

idx = 11
plt.imshow(testX[idx], cmap="gray")
plt.title(testY[idx])
plt.show()

from keras.models import Sequential
from keras.layers import Dense, Flatten
from keras.optimizers import SGD

from sklearn.preprocessing import OneHotEncoder

enc = OneHotEncoder()
trainYOneHot = enc.fit_transform(trainY.reshape(-1, 1)).toarray()

trainValYOneHot = enc.fit_transform(trainValY.reshape(-1, 1)).toarray()

testYOneHot = enc.fit_transform(testY.reshape(-1, 1)).toarray()

from keras.layers import Flatten

model = Sequential()
model.add(Flatten(input_shape=(28, 28)))  # [BS, 28, 28] -> [BS, 28*28]
model.add(Dense(10, activation="softmax"))  # [BS, 28*28] -> [BS, 10]

sgd = SGD(learning_rate=0.05)

model.compile(
    optimizer=sgd,
    loss="categorical_crossentropy",
    metrics=["accuracy"],
)  # 告知模型訓練方式

# 檢視一下所訓練的模型
model.summary()

hist = model.fit(
    trainX,
    trainYOneHot,
    epochs=20,
    batch_size=128,
    validation_data=(trainValX, trainValYOneHot),
)

score = model.evaluate(testX, testYOneHot, batch_size=128)
print()
print("\nloss=%s \naccuracy=%s" % (score[0], score[1]))

_pred_y = (
    model(
        # np.expand_dims(trainValX[j], axis=0), # [H, W] -> [1, H, W]
        trainValX,  # [num_samples, H, W]
    )
    .cpu()
    .detach()
    .numpy()
    .argmax(axis=-1)
)
_true_y = trainValY

accuracy = (_true_y == _pred_y).mean()
accuracy

for j in range(50):
    predY = model.predict(trainValX[j : j + 1, :]).argmax()
    trueY = trainValYOneHot[j].argmax()
    print(predY, trueY, end="\t")

plt.plot(hist.history["accuracy"], ms=5, marker="o", label="accuracy")
plt.plot(hist.history["val_accuracy"], ms=5, marker="o", label="val accuracy")
plt.legend()
plt.show()

import json

with open("first_try.json", "w") as jsOut:
    json.dump(model.to_json(), jsOut)

model.save_weights("first_try.h5")

from keras.models import model_from_json

with open("first_try.json", "r") as jsIn:
    modelJson = json.load(jsIn)

modelLoaded = model_from_json(modelJson)
modelLoaded.load_weights("first_try.h5")

modelLoaded.summary()

from sklearn.metrics import classification_report

predY = model.predict(testX).argmax(axis=1)
print(classification_report(testY, predY))

trainX = trainX.reshape(*trainX.shape, 1)
trainValX = trainValX.reshape(*trainValX.shape, 1)
testX = testX.reshape(*testX.shape, 1)

trainX.shape

from keras.models import Sequential
from keras.layers import Dense, Dropout, Flatten, Conv2D, MaxPooling2D
from keras.layers import Activation
from keras.optimizers import SGD, Adam, Adamax

input_shape = (28, 28, 1)

model = Sequential()

# conv1
model.add(
    Conv2D(
        filters=32,
        kernel_size=(3, 3),
        activation="relu",
        input_shape=input_shape,
        padding="SAME",
    )  # [BS, H=28, W=28, C=1] -> [BS, 28, 28, 32]
)

# conv2
model.add(
    Conv2D(
        filters=64,
        kernel_size=(3, 3),
        activation="relu",
        padding="SAME",
    )  # [BS, 28, 28, 32] ->  [BS, 28, 28, 64]
)


# pool1
model.add(MaxPooling2D(pool_size=(2, 2)))
#  [BS, 28, 28, 64] -> [BS, 14, 14, 64]

# conv3
model.add(
    Conv2D(
        filters=64,
        kernel_size=(3, 3),
        activation="relu",
        padding="SAME",
    )
)
#  [BS, 14, 14, 64] -> [BS, 14, 14, 64]

# pool2
model.add(MaxPooling2D(pool_size=(2, 2)))
# [BS, 14, 14, 64] -> [BS, 7, 7, 64]

# dropout1
model.add(Dropout(0.5))
model.add(Flatten())  # [BS, 7, 7, 64] -> [BS, num_features=7*7*64]
# dense1
model.add(Dense(128, activation="relu"))  #  [BS, num_features=7*7*64] -> [BS, 128]
# dropout2
model.add(Dropout(0.5))
# dense2
model.add(Dense(10, activation="softmax"))  # [BS, 128] -> [BS, num_classes=10]

model.compile(
    loss="categorical_crossentropy",
    optimizer=SGD(learning_rate=0.05),
    metrics=["accuracy"],
)

%%time 
hist = model.fit(trainX, trainYOneHot, 
                 epochs=30,
                 batch_size=128,
                 validation_data=(trainValX,trainValYOneHot),)

plt.plot(hist.history["accuracy"], ms=5, marker="o", label="accuracy")
plt.plot(hist.history["val_accuracy"], ms=5, marker="o", label="val accuracy")
plt.legend()
plt.show()

score = model.evaluate(testX, testYOneHot, batch_size=128)
print()
print("\nloss=%s \naccuracy=%s" % (score[0], score[1]))

from sklearn.metrics import classification_report

predY = model.predict(testX).argmax(axis=1)
print(classification_report(testY, predY))

input_shape = (28, 28, 1)

model = Sequential()

# conv1
model.add(
    Conv2D(
        filters=32,
        kernel_size=(3, 3),
        activation="relu",
        input_shape=input_shape,
        padding="SAME",
    )  # [BS, H=28, W=28, C=1] -> [BS, 28, 28, 32]
)

# conv2
model.add(
    Conv2D(
        filters=64,
        kernel_size=(3, 3),
        activation="relu",
        padding="SAME",
    )  # [BS, 28, 28, 32] ->  [BS, 28, 28, 64]
)


# pool1
model.add(MaxPooling2D(pool_size=(2, 2)))
#  [BS, 28, 28, 64] -> [BS, 14, 14, 64]

# conv3
model.add(
    Conv2D(
        filters=64,
        kernel_size=(3, 3),
        activation="relu",
        padding="SAME",
    )
)
#  [BS, 14, 14, 64] -> [BS, 14, 14, 64]

model.add(
    Conv2D(
        filters=10,
        kernel_size=(1, 1),
        # activation="relu",
        padding="SAME",
    )  # [BS, 14, 14, 64] -> [BS, 14, 14, 10]
)

model.add(keras.layers.GlobalAveragePooling2D())  # [BS, 14, 14, 10] -> [BS, 10]
model.add(keras.layers.Activation("softmax"))
model.summary()
# ... -> [BS, 10]


# # pool2
# model.add(MaxPooling2D(pool_size=(2, 2)))
# # [BS, 14, 14, 64] -> [BS, 7, 7, 64]

# # dropout1
# model.add(Dropout(0.5))
# model.add(Flatten())  # [BS, 7, 7, 64] -> [BS, num_features=7*7*64]
# # dense1
# model.add(Dense(128, activation="relu"))  #  [BS, num_features=7*7*64] -> [BS, 128]
# # dropout2
# model.add(Dropout(0.5))
# # dense2
# model.add(Dense(10, activation="softmax"))  # [BS, 128] -> [BS, num_classes=10]

model.compile(
    loss="categorical_crossentropy",
    optimizer=SGD(learning_rate=0.05),
    metrics=["accuracy"],
)

hist = model.fit(
    trainX,
    trainYOneHot,
    epochs=30,
    batch_size=128,
    validation_data=(trainValX, trainValYOneHot),
)

