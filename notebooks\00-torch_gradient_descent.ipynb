{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["目的: 初步理解如何使用PyTorch進行倒數計算和模型優化。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習1: $y = 3x$，求$\\frac{dy}{dx}|_{x=10}$。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([3.])\n"]}], "source": ["x = torch.tensor(\n", "    [10.0],\n", "    dtype=torch.float32,\n", "    requires_grad=True,\n", ")  # 未來會有某v對x偏微分\n", "\n", "y = 3.0 * x\n", "\n", "y.backward()  # 將會把y去對整張算圖內的, 所有已知要算梯度的權重(或是所謂張量), 去算偏微分\n", "print(x.grad)  # 存y對x偏微分的結果"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習2: $y = 3x^2$，求$\\frac{dy}{dx}|_{x=10}$。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([120.])\n"]}], "source": ["x = torch.tensor(\n", "    [10.0],\n", "    dtype=torch.float32,\n", "    requires_grad=True,\n", ")  # 未來會有某v對x偏微分\n", "\n", "y = 3.0 * (x**2)\n", "\n", "y.backward()  # 將會把y去對整張算圖內的, 所有已知要算梯度的權重(或是所謂張量), 去算偏微分\n", "print(x.grad)  # 存y對x偏微分的結果"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習3: $z = (x+y)^2$，求$\\frac{\\partial z}{\\partial y}|_{(x=10,y=5)}$。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([30.])\n", "tensor([30.])\n"]}], "source": ["# FORWARARD PROPAGATION\n", "\n", "x = torch.tensor(\n", "    [\n", "        10.0,\n", "    ],\n", "    dtype=torch.float32,\n", "    requires_grad=True,\n", ")  # 未來會有?對x偏微分\n", "y = torch.tensor(\n", "    [\n", "        5.0,\n", "    ],\n", "    dtype=torch.float32,\n", "    requires_grad=True,\n", ")  # 未來會有?對y偏微分\n", "u = x + y\n", "z = u**2\n", "\n", "# BACKWARD PROPAGATION\n", "z.backward()  # 將會把y去對整張算圖內的, 所有已知要算梯度的權重(或是所謂張量), 去算偏微分\n", "\n", "print(x.grad)  # 存z對x偏微分的結果\n", "print(y.grad)  # 存z對y偏微分的結果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([30., 30.])\n"]}], "source": ["u = torch.tensor(\n", "    [10.0, 5.0],\n", "    dtype=torch.float32,\n", "    requires_grad=True,\n", ")  # 未來會有?對u偏微分\n", "\n", "z = torch.sum(u) ** 2\n", "\n", "z.backward()  # 將會把y去對整張算圖內的, 所有已知要算梯度的權重(或是所謂張量), 去算偏微分\n", "print(u.grad)  # 存z對u偏微分的結果"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習4: 試用PyTorch計算$\\frac{d\\sin(x)}{dx}$。並將$sin(x)$以及$\\frac{d\\sin(x)}{dx}$透過Matplotlib畫圖。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import torch\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# FORWARD PROPAGATION\n", "x = torch.tensor(\n", "    np.linspace(\n", "        0.0,\n", "        2.0 * np.pi,\n", "        100,\n", "    ),\n", "    requires_grad=True,\n", ")\n", "u = torch.sin(\n", "    x\n", ")  # u 攜帶 [sin(0), sin(0+0.063), sin(0+0.063+0.063), ...., sin(2.*np.pi)]\n", "y = torch.sum(u)  # why this?\n", "\n", "# BACKWARD PROPAGATION\n", "y.backward()  # 使得x.grad攜帶 [cos(0), cos(0+0.063), cos(0+0.063+0.063), ...., cos(2.*np.pi)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["$y=\\sin(x_1) + \\sin(x_2) + ... + \\sin(x_N)$\n", "\n", "$\\frac{\\partial y}{\\partial x_1} = \\frac{\\sin(x_1)}{\\partial x_1}=\\cos(x_1)$\n", "\n", "$\\frac{\\partial y}{\\partial x_2} = \\frac{\\sin(x_2)}{\\partial x_2}=\\cos(x_2)$\n", "..\n", "..\n", "$\\frac{\\partial y}{\\partial x_N} = \\frac{\\sin(x_N)}{\\partial x_N}=\\cos(x_N)$\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(\n", "    x.detach().numpy(),\n", "    u.detach().numpy(),  # sine\n", "    label=\"$\\\\sin(x)$\",\n", "    s=1,\n", ")\n", "\n", "plt.scatter(\n", "    x.detach().numpy(),\n", "    x.grad.numpy(),  # cosine\n", "    label=\"$\\\\cos(x)=\\\\frac{d \\\\sin(x)}{d x}$\",\n", "    s=1,\n", ")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習5: $Loss=|w_1| + |w_2|$。請用Gradient Descent優化器，優化出可將此$Loss$最小化的最適$w_1$和$w_2$。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Find optimal ($w_1$, $w_2$,..., $w_N$) to minimize Loss. \n", "\n", "Loss (Lasso)=$|w_1|+|w_2|+..+|w_N|$\n", "\n", "Answer: ($w_1=0$, $w_2=0$,..., $w_N=0$) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["$L = L_0 + \\lambda * L_{l_1}$ (原來Loss+Lasso懲罰項)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["when $\\lambda * L_{l_1} \\gg L_0$, $L\\approx \\lambda * L_{l_1}$"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x7a71b10cf1d0>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(5, 5))\n", "plt.scatter(w1.detach().numpy(), w2.detach().numpy())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "source": ["degrees = np.linspace(0, 2.0 * np.pi, 100, dtype=np.float32)\n", "r = 5\n", "w1 = r * np.cos(degrees)\n", "w2 = r * np.sin(degrees)\n", "w1_original = np.copy(w1)\n", "w2_original = np.copy(w2)\n", "\n", "w1 = torch.tensor(w1, requires_grad=True)\n", "w2 = torch.tensor(w2, requires_grad=True)\n", "\n", "learning_rate: float = 0.1\n", "opt = torch.optim.SGD(params=[w1, w2], lr=learning_rate)\n", "\n", "num_iterations: int = 5\n", "for iteration_idx in range(num_iterations):\n", "    print(iteration_idx)\n", "\n", "    # FORWARD PROPAGATION\n", "    loss = torch.sum(torch.abs(w1)) + torch.sum(torch.abs(w2))\n", "\n", "    # BACKWARD PROPAGATION\n", "    loss.backward()  # 算出loss分別對於w1和w2的偏微分, 換句話說, 得到w1和w2的梯度\n", "\n", "    # Loss Optimization\n", "    opt.step()  # 透過ooo的梯度, 將ooo梯度下降\n", "\n", "    # opt.zero_grad()  # bad side effect (清掉梯度歷史紀錄, 讓未來沒有機會累加梯度!)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5.       , 4.9899335, 4.959774 , 4.9096437, 4.8397436],\n", "      dtype=float32)"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["w1_original[:5]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([3.5000, 3.4899, 3.4598, 3.4096, 3.3397], grad_fn=<SliceBackward0>)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["w1[:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["w1:\n", "* iteration 0: $g_{w_1}^{0}$\n", "* iteration 1: $g_{w_1}^{1}$\n", "* iteration 2: $g_{w_1}^{2}$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* iteration 0: $g_{w_1}^{0}=0.1$\n", "* iteration 1: $g_{w_1}^{0} + g_{w_1}^{1}=0.1*2$\n", "* iteration 2: $g_{w_1}^{0} + g_{w_1}^{1} + g_{w_1}^{2}=0.1*3$\n", "* iteration 3: $g_{w_1}^{0} + ... + g_{w_1}^{3}=0.1*4$\n", "* iteration 4: $g_{w_1}^{0} + ... + g_{w_1}^{4}=0.1*5$"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.5"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["0.1 + 0.1 * 2 + 0.1 * 3 + 0.1 * 4 + 0.1 * 5"]}], "metadata": {"kernelspec": {"display_name": "dl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}